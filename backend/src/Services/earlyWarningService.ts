import { logInfo, logError, logDebug } from '../utils/logger';
import { prismaService } from './prismaService';
import { BinanceService } from './binanceService';
import { BinanceWhaleDetectionService } from './binanceWhaleDetectionService';
import { AdvancedTechnicalAnalysis } from './advancedTechnicalAnalysis';
import { Server } from 'socket.io';
import { SUPPORTED_TIMEFRAMES } from '../config/coinConfig';


interface EarlyWarningAlert {
  id?: string;
  symbol: string;
  alertType: 'PUMP_LIKELY' | 'DUMP_LIKELY' | 'NEUTRAL';
  confidence: number;
  timeEstimateMin: number;
  timeEstimateMax: number;
  triggeredBy: string[];
  currentPrice: number;
  volume24h?: number;
  priceChange24h?: number;

  // Phase data
  volumeSpike?: any;
  multiTimeframeVolumeAnalysis?: any;
  rsiMomentum?: any;
  emaConvergence?: any;
  bidAskImbalance?: any;
  priceAction?: any;
  whaleActivity?: any;

  // Scores
  phase1Score: number;
  phase2Score: number;
  phase3Score: number;
}

interface WallData {
  price: number;
  quantity: number;
  value: number;
  level: number;
  type: 'BID' | 'ASK';
  firstSeen: number;
  lastSeen: number;
  persistence: number;
  weightedScore: number;
  isLarge: boolean;
}

interface WallAnalysis {
  walls: WallData[];
  wallCount: number;
  totalVolume: number;
  totalWeightedVolume: number;
  totalValue: number;
  averageDistance: number;
  persistentWalls: number;
  largeWalls: number;
  score: number;
}

interface DirectionalAnalysis {
  bidStrength: number;
  askStrength: number;
  imbalanceRatio: number;
  dominantSide: 'BID' | 'ASK' | 'NEUTRAL';
  wallClusters: {
    bidClusters: number;
    askClusters: number;
  };
  persistenceAdvantage: 'BID' | 'ASK' | 'NEUTRAL';
}

interface EnhancedImbalanceScore {
  detected: boolean;
  signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  confidence: number;
  totalScore: number;
  components: {
    depthWeightScore: number;
    wallTypeScore: number;
    persistenceScore: number;
    clusteringScore: number;
  };
  reasoning: string[];
}

interface VolumeAnalysis {
  currentVolume: number;
  avgVolume: number;
  ratio: number;
  priceChange: number;
  acceleration: number;
}

interface BuySellVolumeAnalysis {
  timeframe: string;
  currentBuyVolume: number;
  currentSellVolume: number;
  avgBuyVolume: number;
  avgSellVolume: number;
  buyVolumeRatio: number;
  sellVolumeRatio: number;
  buyVolumeSpike: boolean;
  sellVolumeSpike: boolean;
  dominantDirection: 'BUY' | 'SELL' | 'NEUTRAL';
  signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
}

interface MultiTimeframeVolumeAnalysis {
  symbol: string;
  timeframes: BuySellVolumeAnalysis[];
  overallSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  strongestTimeframe: string | null;
  score: number;
}

interface RSIAnalysis {
  currentRSI: number;
  previousRSI: number;
  velocity: number;
  momentum: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
}

interface EMAAnalysis {
  ema20: number;
  ema50: number;
  gap: number;
  gapPercent: number;
  momentum: number;
  convergence: boolean;
}

export class EarlyWarningService {
  private binanceService: BinanceService;
  private whaleDetectionService: BinanceWhaleDetectionService;
  private technicalAnalysis: AdvancedTechnicalAnalysis;
  private io?: Server;

  private volumeHistory: Map<string, number[]> = new Map();
  private rsiHistory: Map<string, number[]> = new Map();
  private priceHistory: Map<string, number[]> = new Map();
  private lastAlerts: Map<string, number> = new Map(); // Prevent spam
  private wallTracker: Map<string, Map<string, WallData>> = new Map(); // symbol -> wallKey -> WallData

  constructor(binanceService: BinanceService, technicalAnalysis: AdvancedTechnicalAnalysis, io?: Server) {
    this.binanceService = binanceService;
    this.whaleDetectionService = new BinanceWhaleDetectionService(binanceService);
    this.technicalAnalysis = technicalAnalysis;
    this.io = io;

  }

  // Main method to analyze symbol for early warnings
  async analyzeSymbol(symbol: string): Promise<EarlyWarningAlert | null> {
    try {
      // Get current market data
      const ticker = this.binanceService.getCachedPrice(symbol);
      if (!ticker) {
        logDebug(`No cached price data for ${symbol}`);
        return null;
      }

      const currentPrice = parseFloat(ticker.price);
      const volume24h = parseFloat(ticker.volume);
      const priceChange24h = parseFloat(ticker.priceChangePercent);

      // Phase 1: Volume & Momentum Detection
      const phase1Result = await this.analyzePhase1(symbol, currentPrice, volume24h, priceChange24h);
      
      // Phase 2: Order Flow Analysis (if order book data available)
      const phase2Result = await this.analyzePhase2(symbol);
      
      // Phase 3: Whale Activity (placeholder for now)
      const phase3Result = await this.analyzePhase3(symbol);

      // Calculate overall confidence and alert type
      const alert = this.calculateOverallAlert(
        symbol, currentPrice, volume24h, priceChange24h,
        phase1Result, phase2Result, phase3Result
      );

      // Only process PUMP_LIKELY and DUMP_LIKELY signals, skip NEUTRAL
      // Note: Alerts are only saved to DB and broadcasted when they match configured rules
      // This is handled by the early warning alert rules cron job
      if (alert && alert.alertType !== 'NEUTRAL' && alert.confidence > 0 && this.shouldCreateAlert(symbol, alert.alertType)) {
        // Return alert for rule checking, but don't save to DB or broadcast yet
        // Only rule-matched alerts will be saved and broadcasted
        logInfo(`Returning valid alert for ${symbol}:`, {
          alertType: alert.alertType,
          confidence: alert.confidence,
          triggeredBy: alert.triggeredBy
        });
        return alert;
      }

      // Log why alert was rejected
      if (alert) {
        logInfo(`Alert rejected for ${symbol}:`, {
          alertType: alert.alertType,
          confidence: alert.confidence,
          triggeredBy: alert.triggeredBy,
          reason: alert.alertType === 'NEUTRAL' ? 'NEUTRAL alert type' :
                  alert.confidence <= 0 ? 'Zero confidence' :
                  'Spam prevention (too recent)'
        });
      }

      return null;
    } catch (error) {
      logError(`Error analyzing ${symbol} for early warnings`, error as Error);
      return null;
    }
  }

  // Phase 1: Volume & Momentum Detection (Max 25 points total)
  private async analyzePhase1(symbol: string, currentPrice: number, volume24h: number, priceChange24h: number) {
    const results = {
      volumeSpike: null as any,
      multiTimeframeVolumeAnalysis: null as any,
      rsiMomentum: null as any,
      emaConvergence: null as any,
      score: 0,
      subComponentScores: {
        multiTimeframeVolume: 0,
        rsiMomentum: 0,
        emaConvergence: 0
      },
      overallSignal: 'NEUTRAL' as 'BULLISH' | 'BEARISH' | 'NEUTRAL'
    };

    let volumeScore = 0;
    let rsiScore = 0;
    let emaScore = 0;
    let volumeSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    let rsiSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    let emaSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';

    try {
      // 1. Multi-Timeframe Buy/Sell Volume Spike Detection (Max 15 points out of 25)
      const multiTimeframeVolumeAnalysis = await this.analyzeMultiTimeframeVolumeSpikes(symbol);

      // Only mark as detected if there are actual volume spikes or strong signals
      const hasSignificantVolumeActivity = multiTimeframeVolumeAnalysis.timeframes.some(tf =>
        tf.buyVolumeSpike || tf.sellVolumeSpike || tf.signal !== 'NEUTRAL'
      ) || multiTimeframeVolumeAnalysis.overallSignal !== 'NEUTRAL';

      if (multiTimeframeVolumeAnalysis.timeframes.length > 0 && hasSignificantVolumeActivity) {
        results.multiTimeframeVolumeAnalysis = {
          detected: true,
          ...multiTimeframeVolumeAnalysis
        };

        // Volume analysis gets 15 points out of 25
        volumeScore = 15;
        volumeSignal = multiTimeframeVolumeAnalysis.overallSignal || 'NEUTRAL';
        results.subComponentScores.multiTimeframeVolume = volumeScore;

        // Keep legacy volumeSpike for backward compatibility (use strongest timeframe)
        if (multiTimeframeVolumeAnalysis.strongestTimeframe) {
          const strongestAnalysis = multiTimeframeVolumeAnalysis.timeframes.find(
            (tf: BuySellVolumeAnalysis) => tf.timeframe === multiTimeframeVolumeAnalysis.strongestTimeframe
          );

          if (strongestAnalysis && (strongestAnalysis.buyVolumeSpike || strongestAnalysis.sellVolumeSpike)) {
            results.volumeSpike = {
              detected: true,
              timeframe: strongestAnalysis.timeframe,
              signal: strongestAnalysis.signal,
              buyVolumeRatio: strongestAnalysis.buyVolumeRatio,
              sellVolumeRatio: strongestAnalysis.sellVolumeRatio,
              dominantDirection: strongestAnalysis.dominantDirection,
              // Include actual volume data for frontend display
              currentVolume: strongestAnalysis.currentBuyVolume + strongestAnalysis.currentSellVolume,
              averageVolume: strongestAnalysis.avgBuyVolume + strongestAnalysis.avgSellVolume,
              currentBuyVolume: strongestAnalysis.currentBuyVolume,
              currentSellVolume: strongestAnalysis.currentSellVolume,
              avgBuyVolume: strongestAnalysis.avgBuyVolume,
              avgSellVolume: strongestAnalysis.avgSellVolume
            };
          }
        }
      }

      // 2. RSI Momentum Analysis (Max 7 points out of 25)
      const rsiAnalysis = await this.analyzeRSIMomentum(symbol);
      if (rsiAnalysis && Math.abs(rsiAnalysis.velocity) > 5) {
        results.rsiMomentum = {
          detected: true,
          ...rsiAnalysis
        };
        // RSI gets 7 points out of 25
        rsiScore = 7;
        rsiSignal = rsiAnalysis.momentum || 'NEUTRAL';
        results.subComponentScores.rsiMomentum = rsiScore;
      }

      // 3. EMA Convergence Detection (Max 3 points out of 25)
      const emaAnalysis = await this.analyzeEMAConvergence(symbol);
      if (emaAnalysis && emaAnalysis.convergence && Math.abs(emaAnalysis.gapPercent) < 0.5) {
        results.emaConvergence = {
          detected: true,
          ...emaAnalysis
        };
        // EMA gets 3 points out of 25 (lowest precedence)
        emaScore = 3;
        // EMA convergence doesn't provide directional signal, so it stays NEUTRAL
        emaSignal = 'NEUTRAL';
        results.subComponentScores.emaConvergence = emaScore;
      }

      // Calculate directional score for Phase 1
      let directionalScore = 0;

      // Apply directional scoring for volume analysis
      if (volumeSignal === 'BULLISH') {
        directionalScore += volumeScore;
      } else if (volumeSignal === 'BEARISH') {
        directionalScore -= volumeScore;
      }

      // Apply directional scoring for RSI momentum
      if (rsiSignal === 'BULLISH') {
        directionalScore += rsiScore;
      } else if (rsiSignal === 'BEARISH') {
        directionalScore -= rsiScore;
      }

      // EMA convergence doesn't contribute directionally (just indicates volatility)
      // But we add it to the absolute score for legacy compatibility

      // Determine overall Phase 1 signal based on net directional score
      if (directionalScore > 0) {
        results.overallSignal = 'BULLISH';
      } else if (directionalScore < 0) {
        results.overallSignal = 'BEARISH';
      } else {
        results.overallSignal = 'NEUTRAL';
      }

      // Store absolute score including EMA (for legacy compatibility)
      results.score = Math.abs(directionalScore) + emaScore;

    } catch (error) {
      logError(`Error in Phase 1 analysis for ${symbol}`, error as Error);
    }

    logInfo(`Phase 1 Analysis for ${symbol}:`, {
      volumeSpike: results.volumeSpike?.detected || false,
      multiTimeframeVolumeAnalysis: results.multiTimeframeVolumeAnalysis?.detected || false,
      timeframesAnalyzed: results.multiTimeframeVolumeAnalysis?.timeframes?.length || 0,
      overallVolumeSignal: results.multiTimeframeVolumeAnalysis?.overallSignal || 'NEUTRAL',
      strongestTimeframe: results.multiTimeframeVolumeAnalysis?.strongestTimeframe || null,
      rsiMomentum: results.rsiMomentum?.detected || false,
      emaConvergence: results.emaConvergence?.detected || false,
      score: results.score
    });

    return results;
  }

  // Volume spike detection
  private async analyzeVolumeSpike(symbol: string, currentVolume: number, priceChange: number): Promise<VolumeAnalysis> {
    // Get historical volume data
    const volumeKey = `${symbol}_volume`;
    let volumeHistory = this.volumeHistory.get(volumeKey) || [];
    
    // Add current volume
    volumeHistory.push(currentVolume);
    
    // Keep only last 20 periods for moving average
    if (volumeHistory.length > 20) {
      volumeHistory = volumeHistory.slice(-20);
    }
    
    this.volumeHistory.set(volumeKey, volumeHistory);
    
    // Calculate 20-period average
    const avgVolume = volumeHistory.reduce((sum, vol) => sum + vol, 0) / volumeHistory.length;
    const ratio = currentVolume / avgVolume;
    
    // Calculate acceleration (volume trend over last 3-5 periods)
    const recentVolumes = volumeHistory.slice(-5);
    const acceleration = recentVolumes.length > 1 ? 
      (recentVolumes[recentVolumes.length - 1] - recentVolumes[0]) / recentVolumes[0] : 0;

    return {
      currentVolume,
      avgVolume,
      ratio,
      priceChange,
      acceleration
    };
  }

  // Multi-timeframe buy/sell volume spike analysis
  private async analyzeMultiTimeframeVolumeSpikes(symbol: string): Promise<MultiTimeframeVolumeAnalysis> {
    const timeframeAnalyses: BuySellVolumeAnalysis[] = [];
    let totalScore = 0;
    let strongestTimeframe: string | null = null;
    let maxScore = 0;

    // Analyze volume spikes across all supported timeframes
    for (const timeframe of SUPPORTED_TIMEFRAMES) {
      try {
        const analysis = await this.analyzeBuySellVolumeSpike(symbol, timeframe);
        if (analysis) {
          timeframeAnalyses.push(analysis);

          // Calculate score for this timeframe
          const timeframeScore = this.calculateTimeframeVolumeScore(analysis);
          totalScore += timeframeScore;

          if (timeframeScore > maxScore) {
            maxScore = timeframeScore;
            strongestTimeframe = timeframe;
          }
        }
      } catch (error) {
        logError(`Error analyzing volume for ${symbol} ${timeframe}`, error as Error);
      }
    }

    // Determine overall signal based on timeframe analyses
    const overallSignal = this.determineOverallVolumeSignal(timeframeAnalyses);

    return {
      symbol,
      timeframes: timeframeAnalyses,
      overallSignal,
      strongestTimeframe,
      score: totalScore
    };
  }

  // Analyze buy/sell volume spikes for a specific timeframe
  private async analyzeBuySellVolumeSpike(symbol: string, timeframe: string): Promise<BuySellVolumeAnalysis | null> {
    try {
      // Check if we have sufficient data for this timeframe
      if (!this.binanceService.hasKlineData(symbol, timeframe)) {
        logDebug(`No kline data available for ${symbol} ${timeframe}`);
        return null;
      }

      // Get buy/sell volume data from Binance service
      const volumeData = this.binanceService.getBuySellVolumeData(symbol, timeframe, 100);

      if (volumeData.buyVolumes.length < 50) {
        logDebug(`Insufficient volume data for ${symbol} ${timeframe} (${volumeData.buyVolumes.length} periods)`);
        return null;
      }

      // Calculate current and average volumes
      const currentBuyVolume = volumeData.buyVolumes[volumeData.buyVolumes.length - 1];
      const currentSellVolume = volumeData.sellVolumes[volumeData.sellVolumes.length - 1];

      // Use 100-period average as requested
      const avgBuyVolume = volumeData.buyVolumes.reduce((sum, vol) => sum + vol, 0) / volumeData.buyVolumes.length;
      const avgSellVolume = volumeData.sellVolumes.reduce((sum, vol) => sum + vol, 0) / volumeData.sellVolumes.length;

      // Calculate spike ratios
      const buyVolumeRatio = avgBuyVolume > 0 ? currentBuyVolume / avgBuyVolume : 0;
      const sellVolumeRatio = avgSellVolume > 0 ? currentSellVolume / avgSellVolume : 0;

      // Determine if spikes are significant (threshold: 1.8x average for more sensitivity)
      const buyVolumeSpike = buyVolumeRatio > 1.8;
      const sellVolumeSpike = sellVolumeRatio > 1.8;

      // Determine dominant direction and signal
      const { dominantDirection, signal } = this.determineBuySellSignal(
        buyVolumeRatio,
        sellVolumeRatio,
        buyVolumeSpike,
        sellVolumeSpike
      );

      return {
        timeframe,
        currentBuyVolume,
        currentSellVolume,
        avgBuyVolume,
        avgSellVolume,
        buyVolumeRatio,
        sellVolumeRatio,
        buyVolumeSpike,
        sellVolumeSpike,
        dominantDirection,
        signal
      };

    } catch (error) {
      logError(`Error analyzing buy/sell volume for ${symbol} ${timeframe}`, error as Error);
      return null;
    }
  }

  // Determine buy/sell signal based on volume ratios and spikes
  private determineBuySellSignal(
    buyRatio: number,
    sellRatio: number,
    buySpike: boolean,
    sellSpike: boolean
  ): { dominantDirection: 'BUY' | 'SELL' | 'NEUTRAL', signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' } {

    // Strong buy signal: buy volume spike with high ratio
    if (buySpike && buyRatio > sellRatio && buyRatio > 2.2) {
      return { dominantDirection: 'BUY', signal: 'BULLISH' };
    }

    // Strong sell signal: sell volume spike with high ratio
    if (sellSpike && sellRatio > buyRatio && sellRatio > 2.2) {
      return { dominantDirection: 'SELL', signal: 'BEARISH' };
    }

    // Buy spike alone (even if sell ratio is higher)
    if (buySpike && buyRatio > 1.8) {
      return { dominantDirection: 'BUY', signal: 'BULLISH' };
    }

    // Sell spike alone (even if buy ratio is higher)
    if (sellSpike && sellRatio > 1.8) {
      return { dominantDirection: 'SELL', signal: 'BEARISH' };
    }

    // Moderate buy signal: buy volume higher than sell
    if (buyRatio > sellRatio && buyRatio > 1.3) {
      return { dominantDirection: 'BUY', signal: 'BULLISH' };
    }

    // Moderate sell signal: sell volume higher than buy
    if (sellRatio > buyRatio && sellRatio > 1.3) {
      return { dominantDirection: 'SELL', signal: 'BEARISH' };
    }

    // Neutral: no clear dominance
    return { dominantDirection: 'NEUTRAL', signal: 'NEUTRAL' };
  }

  // Calculate score for a timeframe's volume analysis (max 8 points for Phase 1)
  private calculateTimeframeVolumeScore(analysis: BuySellVolumeAnalysis): number {
    let score = 0;

    // Base score for any spike (reduced from 15 to 3)
    if (analysis.buyVolumeSpike) score += 3;
    if (analysis.sellVolumeSpike) score += 3;

    // Bonus for high ratios (reduced from 10 to 2)
    if (analysis.buyVolumeRatio > 3.0) score += 2;
    if (analysis.sellVolumeRatio > 3.0) score += 2;

    // Bonus for very high ratios (reduced from 15 to 3)
    if (analysis.buyVolumeRatio > 5.0) score += 3;
    if (analysis.sellVolumeRatio > 5.0) score += 3;

    // Timeframe weight (shorter timeframes get higher weight for early detection)
    const timeframeWeights: { [key: string]: number } = {
      '5m': 1.2,
      '15m': 1.1,
      '1h': 1.0,
      '4h': 0.9,
      '1d': 0.8
    };

    const weight = timeframeWeights[analysis.timeframe] || 1.0;
    const weightedScore = Math.round(score * weight);

    // Cap individual timeframe score at 8 points (volume's max allocation)
    return Math.min(weightedScore, 8);
  }

  // Calculate RSI momentum score (max 12 points for Phase 1)
  private calculateRSIScore(rsiAnalysis: RSIAnalysis): number {
    const velocity = Math.abs(rsiAnalysis.velocity);
    const currentRSI = rsiAnalysis.currentRSI;

    let score = 0;

    // Base score for velocity (higher velocity = higher score)
    if (velocity > 5) score += 4;
    if (velocity > 10) score += 3;
    if (velocity > 15) score += 2;

    // Bonus for RSI in momentum zones
    if (rsiAnalysis.momentum === 'BULLISH' || rsiAnalysis.momentum === 'BEARISH') {
      score += 3;

      // Extra bonus for extreme momentum (RSI crossing key levels)
      if ((rsiAnalysis.momentum === 'BULLISH' && currentRSI > 60) ||
          (rsiAnalysis.momentum === 'BEARISH' && currentRSI < 40)) {
        score += 2;
      }
    }

    return Math.min(score, 12); // Cap at 12 points
  }

  // Calculate EMA convergence score (max 5 points for Phase 1)
  private calculateEMAScore(emaAnalysis: EMAAnalysis): number {
    const gapPercent = emaAnalysis.gapPercent;
    const momentum = Math.abs(emaAnalysis.momentum);

    let score = 0;

    // Base score for convergence (tighter gap = higher score)
    if (gapPercent < 0.5) score += 2;
    if (gapPercent < 0.3) score += 1;
    if (gapPercent < 0.1) score += 1;

    // Bonus for momentum (gap changing rapidly)
    if (momentum > 0) score += 1;

    return Math.min(score, 5); // Cap at 5 points
  }

  // Determine overall signal from multiple timeframe analyses
  private determineOverallVolumeSignal(analyses: BuySellVolumeAnalysis[]): 'BULLISH' | 'BEARISH' | 'NEUTRAL' {
    if (analyses.length === 0) return 'NEUTRAL';

    let bullishCount = 0;
    let bearishCount = 0;
    let bullishWeight = 0;
    let bearishWeight = 0;

    // Weight signals by timeframe importance
    const timeframeWeights: { [key: string]: number } = {
      '5m': 1.2,
      '15m': 1.1,
      '1h': 1.0,
      '4h': 0.9,
      '1d': 0.8
    };

    for (const analysis of analyses) {
      const weight = timeframeWeights[analysis.timeframe] || 1.0;

      if (analysis.signal === 'BULLISH') {
        bullishCount++;
        bullishWeight += weight;
      } else if (analysis.signal === 'BEARISH') {
        bearishCount++;
        bearishWeight += weight;
      }
    }

    // More lenient signal determination
    // Strong signal: at least 2 timeframes agreeing
    if (bullishCount >= 2 && bullishWeight > bearishWeight * 1.3) {
      return 'BULLISH';
    }

    if (bearishCount >= 2 && bearishWeight > bullishWeight * 1.3) {
      return 'BEARISH';
    }

    // Moderate signal: single strong timeframe with high weight
    if (bullishCount >= 1 && bullishWeight > bearishWeight * 2.0) {
      return 'BULLISH';
    }

    if (bearishCount >= 1 && bearishWeight > bullishWeight * 2.0) {
      return 'BEARISH';
    }

    return 'NEUTRAL';
  }

  // RSI momentum analysis
  private async analyzeRSIMomentum(symbol: string): Promise<RSIAnalysis | null> {
    try {
      // Get RSI from 1m and 5m timeframes
      const indicators1m = await this.technicalAnalysis.calculateIndicators('binance', symbol, '1m');
      const indicators5m = await this.technicalAnalysis.calculateIndicators('binance', symbol, '5m');
      
      const currentRSI = indicators1m.rsi;
      
      // Get RSI history
      const rsiKey = `${symbol}_rsi`;
      let rsiHistory = this.rsiHistory.get(rsiKey) || [];
      rsiHistory.push(currentRSI);
      
      if (rsiHistory.length > 10) {
        rsiHistory = rsiHistory.slice(-10);
      }
      
      this.rsiHistory.set(rsiKey, rsiHistory);
      
      if (rsiHistory.length < 2) return null;
      
      const previousRSI = rsiHistory[rsiHistory.length - 2];
      const velocity = currentRSI - previousRSI;
      
      let momentum: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
      
      // Pump signal: RSI crosses above 50 with momentum (was below 40 recently)
      if (currentRSI > 50 && velocity > 0 && rsiHistory.some(rsi => rsi < 40)) {
        momentum = 'BULLISH';
      }
      // Dump signal: RSI crosses below 50 with momentum (was above 60 recently)
      else if (currentRSI < 50 && velocity < 0 && rsiHistory.some(rsi => rsi > 60)) {
        momentum = 'BEARISH';
      }
      
      return {
        currentRSI,
        previousRSI,
        velocity,
        momentum
      };
      
    } catch (error) {
      logError(`Error analyzing RSI momentum for ${symbol}`, error as Error);
      return null;
    }
  }

  // EMA convergence analysis
  private async analyzeEMAConvergence(symbol: string): Promise<EMAAnalysis | null> {
    try {
      const indicators = await this.technicalAnalysis.calculateIndicators('binance', symbol, '5m');
      
      const ema20 = indicators.ema20;
      const ema50 = indicators.ema50;
      
      if (!ema20 || !ema50) return null;
      
      const gap = Math.abs(ema20 - ema50);
      const gapPercent = (gap / ema50) * 100;
      
      // Calculate momentum (rate of gap change)
      const priceKey = `${symbol}_ema_gap`;
      let gapHistory = this.priceHistory.get(priceKey) || [];
      gapHistory.push(gap);
      
      if (gapHistory.length > 5) {
        gapHistory = gapHistory.slice(-5);
      }
      
      this.priceHistory.set(priceKey, gapHistory);
      
      const momentum = gapHistory.length > 1 ? 
        gapHistory[gapHistory.length - 1] - gapHistory[0] : 0;
      
      const convergence = gapPercent < 0.5 && Math.abs(momentum) > 0;
      
      return {
        ema20,
        ema50,
        gap,
        gapPercent,
        momentum,
        convergence
      };
      
    } catch (error) {
      logError(`Error analyzing EMA convergence for ${symbol}`, error as Error);
      return null;
    }
  }

  // Phase 2: Order Flow Analysis (Max 35 points total)
  private async analyzePhase2(symbol: string) {
    const results = {
      bidAskImbalance: null as any,
      priceAction: null as any,
      score: 0,
      subComponentScores: {
        bidAskImbalance: 0,
        priceAction: 0
      },
      overallSignal: 'NEUTRAL' as 'BULLISH' | 'BEARISH' | 'NEUTRAL'
    };

    let bidAskScore = 0;
    let priceActionScore = 0;
    let bidAskSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    let priceActionSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';

    try {
      // 1. Bid/Ask Imbalance Analysis (Max 25 points out of 35)
      const imbalanceAnalysis = await this.analyzeBidAskImbalance(symbol);
      if (imbalanceAnalysis && imbalanceAnalysis.detected) {
        results.bidAskImbalance = imbalanceAnalysis;
        bidAskScore = 25; // Fixed 25 points for bid/ask imbalance
        bidAskSignal = (imbalanceAnalysis.signal as 'BULLISH' | 'BEARISH' | 'NEUTRAL') || 'NEUTRAL';
        results.subComponentScores.bidAskImbalance = bidAskScore;
      }

      // 2. Price Action Microstructure (Max 10 points out of 35)
      const priceActionAnalysis = await this.analyzePriceActionMicrostructure(symbol);
      if (priceActionAnalysis && priceActionAnalysis.detected) {
        results.priceAction = priceActionAnalysis;
        priceActionScore = 10; // Fixed 10 points for price action
        priceActionSignal = (priceActionAnalysis.signal as 'BULLISH' | 'BEARISH' | 'NEUTRAL') || 'NEUTRAL';
        results.subComponentScores.priceAction = priceActionScore;
      }

      // Calculate directional score for Phase 2
      let directionalScore = 0;

      // Apply directional scoring for bid/ask imbalance
      if (bidAskSignal === 'BULLISH') {
        directionalScore += bidAskScore;
      } else if (bidAskSignal === 'BEARISH') {
        directionalScore -= bidAskScore;
      }

      // Apply directional scoring for price action
      if (priceActionSignal === 'BULLISH') {
        directionalScore += priceActionScore;
      } else if (priceActionSignal === 'BEARISH') {
        directionalScore -= priceActionScore;
      }

      // Determine overall Phase 2 signal based on net directional score
      if (directionalScore > 0) {
        results.overallSignal = 'BULLISH';
      } else if (directionalScore < 0) {
        results.overallSignal = 'BEARISH';
      } else {
        results.overallSignal = 'NEUTRAL';
      }

      // Store absolute score for legacy compatibility
      results.score = Math.abs(directionalScore);

    } catch (error) {
      logError(`Error in Phase 2 analysis for ${symbol}`, error as Error);
    }

    logInfo(`Phase 2 Analysis for ${symbol}:`, {
      bidAskImbalance: results.bidAskImbalance?.detected || false,
      bidAskSignal,
      priceAction: results.priceAction?.detected || false,
      priceActionSignal,
      overallSignal: results.overallSignal,
      directionalScore: results.overallSignal === 'BULLISH' ? results.score :
                       results.overallSignal === 'BEARISH' ? -results.score : 0,
      score: results.score
    });

    return results;
  }

  // Analyze bid/ask imbalance for early pump/dump signals
  private async analyzeBidAskImbalance(symbol: string) {
    try {
      // Get current order book data
      const orderBook = this.binanceService.getCachedOrderBook(symbol);
      if (!orderBook || !orderBook.bids.length || !orderBook.asks.length) {
        // Try to get fresh order book data
        try {
          await this.binanceService.getOrderBook(symbol, 20);
          const freshOrderBook = this.binanceService.getCachedOrderBook(symbol);
          if (!freshOrderBook) return null;
          return this.calculateImbalance(freshOrderBook);
        } catch (error) {
          return null;
        }
      }

      return this.calculateImbalance(orderBook);

    } catch (error) {
      logError(`Error analyzing bid/ask imbalance for ${symbol}`, error as Error);
      return null;
    }
  }

  // Enhanced order book imbalance calculation with wall detection and persistence tracking
  private calculateImbalance(orderBook: any) {
    const topBids = orderBook.bids.slice(0, 10);
    const topAsks = orderBook.asks.slice(0, 10);
    const symbol = orderBook.symbol || 'UNKNOWN';
    const now = Date.now();

    // Initialize wall tracking for this symbol if not exists
    if (!this.wallTracker.has(symbol)) {
      this.wallTracker.set(symbol, new Map());
    }
    const wallHistory = this.wallTracker.get(symbol)!;

    // Calculate current price for distance weighting
    const currentPrice = (topBids[0].price + topAsks[0].price) / 2;

    // Priority 1: Depth-Weighted Wall Detection
    const bidWallAnalysis = this.analyzeWalls(topBids, 'BID', currentPrice, wallHistory, now);
    const askWallAnalysis = this.analyzeWalls(topAsks, 'ASK', currentPrice, wallHistory, now);

    // Priority 2: Wall Type Classification & Directional Analysis
    const directionalAnalysis = this.calculateDirectionalSignal(bidWallAnalysis, askWallAnalysis);

    // Priority 3: Wall Persistence Tracking (cleanup old walls)
    this.cleanupStaleWalls(wallHistory, now);

    // Priority 4: Enhanced Scoring System
    const enhancedScore = this.calculateEnhancedImbalanceScore(bidWallAnalysis, askWallAnalysis, directionalAnalysis);

    // Legacy calculations for backward compatibility
    const bidVolume = topBids.reduce((sum: number, bid: any) => sum + bid.quantity, 0);
    const askVolume = topAsks.reduce((sum: number, ask: any) => sum + ask.quantity, 0);
    const totalVolume = bidVolume + askVolume;
    const buyPressure = bidVolume / totalVolume;
    const sellPressure = askVolume / totalVolume;
    const ratio = bidVolume / askVolume;

    return {
      detected: enhancedScore.detected,
      buyPressure,
      sellPressure,
      ratio,
      bidVolume,
      askVolume,
      weightedBidVolume: bidWallAnalysis.totalWeightedVolume,
      weightedAskVolume: askWallAnalysis.totalWeightedVolume,
      largeBids: bidWallAnalysis.wallCount,
      largeAsks: askWallAnalysis.wallCount,
      signal: enhancedScore.signal,
      // Enhanced analysis data
      bidWalls: bidWallAnalysis,
      askWalls: askWallAnalysis,
      directionalAnalysis,
      enhancedScore,
      wallPersistence: {
        totalTrackedWalls: wallHistory.size,
        persistentWalls: Array.from(wallHistory.values()).filter(w => w.persistence >= 30).length
      }
    };
  }

  // Priority 1: Depth-Weighted Wall Detection
  private analyzeWalls(
    orders: any[],
    type: 'BID' | 'ASK',
    currentPrice: number,
    wallHistory: Map<string, WallData>,
    now: number
  ): WallAnalysis {
    const walls: WallData[] = [];
    let totalVolume = 0;
    let totalWeightedVolume = 0;
    let totalValue = 0;
    let wallCount = 0;
    let persistentWalls = 0;
    let largeWalls = 0;

    const LARGE_WALL_THRESHOLD = 100000; // $100k USD
    const MIN_PERSISTENCE_SECONDS = 30;

    orders.forEach((order, index) => {
      const price = parseFloat(order.price);
      const quantity = parseFloat(order.quantity);
      const value = price * quantity;
      const level = index + 1;

      // Calculate distance weight (closer to current price = higher weight)
      const distancePercent = Math.abs(price - currentPrice) / currentPrice;
      const distanceWeight = Math.max(0.1, 1 - (distancePercent * 10)); // Level 1 = 100%, decreases with distance

      // Calculate depth weight (level-based)
      const depthWeight = Math.max(0.1, 1 - (level - 1) * 0.2); // Level 1 = 100%, Level 2 = 80%, etc.

      // Combined weight
      const combinedWeight = distanceWeight * depthWeight;
      const weightedVolume = quantity * combinedWeight;
      const weightedScore = value * combinedWeight;

      // Check if this is a large wall
      const isLarge = value >= LARGE_WALL_THRESHOLD;

      // Create wall key for persistence tracking
      const wallKey = `${type}_${price.toFixed(8)}`;
      let wallData = wallHistory.get(wallKey);

      if (!wallData) {
        // New wall detected
        wallData = {
          price,
          quantity,
          value,
          level,
          type,
          firstSeen: now,
          lastSeen: now,
          persistence: 0,
          weightedScore,
          isLarge
        };
        wallHistory.set(wallKey, wallData);
      } else {
        // Update existing wall
        wallData.quantity = quantity;
        wallData.value = value;
        wallData.level = level;
        wallData.lastSeen = now;
        wallData.persistence = (now - wallData.firstSeen) / 1000; // seconds
        wallData.weightedScore = weightedScore;
        wallData.isLarge = isLarge;
      }

      walls.push(wallData);
      totalVolume += quantity;
      totalWeightedVolume += weightedVolume;
      totalValue += value;

      if (isLarge) {
        wallCount++;
        largeWalls++;
      }

      if (wallData.persistence >= MIN_PERSISTENCE_SECONDS) {
        persistentWalls++;
      }
    });

    // Calculate average distance from current price
    const averageDistance = walls.length > 0
      ? walls.reduce((sum, wall) => sum + Math.abs(wall.price - currentPrice), 0) / walls.length
      : 0;

    // Calculate wall analysis score
    const score = this.calculateWallScore(walls, totalWeightedVolume, persistentWalls, largeWalls);

    return {
      walls,
      wallCount,
      totalVolume,
      totalWeightedVolume,
      totalValue,
      averageDistance,
      persistentWalls,
      largeWalls,
      score
    };
  }

  // Calculate wall score based on multiple factors
  private calculateWallScore(walls: WallData[], totalWeightedVolume: number, persistentWalls: number, largeWalls: number): number {
    let score = 0;

    // Base score from weighted volume
    score += Math.min(totalWeightedVolume / 10000, 30); // Max 30 points

    // Persistence bonus
    score += persistentWalls * 5; // 5 points per persistent wall

    // Large wall bonus
    score += largeWalls * 10; // 10 points per large wall

    // Level proximity bonus (walls closer to current price get bonus)
    const level1Walls = walls.filter(w => w.level === 1).length;
    const level2Walls = walls.filter(w => w.level === 2).length;
    score += level1Walls * 15; // 15 points for level 1 walls
    score += level2Walls * 10; // 10 points for level 2 walls

    return Math.round(score);
  }

  // Priority 2: Wall Type Classification & Directional Analysis
  private calculateDirectionalSignal(bidWallAnalysis: WallAnalysis, askWallAnalysis: WallAnalysis): DirectionalAnalysis {
    const bidStrength = bidWallAnalysis.score;
    const askStrength = askWallAnalysis.score;

    // Calculate imbalance ratio (bid strength vs ask strength)
    const imbalanceRatio = askStrength > 0 ? bidStrength / askStrength : bidStrength > 0 ? 10 : 1;

    // Determine dominant side (more lenient thresholds)
    let dominantSide: 'BID' | 'ASK' | 'NEUTRAL' = 'NEUTRAL';
    if (imbalanceRatio > 1.2) dominantSide = 'BID';
    else if (imbalanceRatio < 0.83) dominantSide = 'ASK';

    // Calculate wall clusters (groups of walls at similar price levels)
    const bidClusters = this.calculateWallClusters(bidWallAnalysis.walls);
    const askClusters = this.calculateWallClusters(askWallAnalysis.walls);

    // Determine persistence advantage
    let persistenceAdvantage: 'BID' | 'ASK' | 'NEUTRAL' = 'NEUTRAL';
    if (bidWallAnalysis.persistentWalls > askWallAnalysis.persistentWalls) {
      persistenceAdvantage = 'BID';
    } else if (askWallAnalysis.persistentWalls > bidWallAnalysis.persistentWalls) {
      persistenceAdvantage = 'ASK';
    }

    return {
      bidStrength,
      askStrength,
      imbalanceRatio,
      dominantSide,
      wallClusters: {
        bidClusters,
        askClusters
      },
      persistenceAdvantage
    };
  }

  // Calculate wall clusters (groups of walls at similar price levels)
  private calculateWallClusters(walls: WallData[]): number {
    if (walls.length < 2) return 0;

    let clusters = 0;
    const sortedWalls = walls.sort((a, b) => a.price - b.price);

    for (let i = 1; i < sortedWalls.length; i++) {
      const priceDiff = Math.abs(sortedWalls[i].price - sortedWalls[i-1].price);
      const avgPrice = (sortedWalls[i].price + sortedWalls[i-1].price) / 2;
      const diffPercent = (priceDiff / avgPrice) * 100;

      // If walls are within 0.1% of each other, consider them clustered
      if (diffPercent <= 0.1) {
        clusters++;
      }
    }

    return clusters;
  }

  // Priority 3: Wall Persistence Tracking
  private cleanupStaleWalls(wallHistory: Map<string, WallData>, currentTime: number): void {
    const staleThreshold = 120000; // 2 minutes
    const keysToDelete: string[] = [];

    wallHistory.forEach((wall, key) => {
      if (currentTime - wall.lastSeen > staleThreshold) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => wallHistory.delete(key));
  }

  // Priority 4: Enhanced Scoring System
  private calculateEnhancedImbalanceScore(
    bidWallAnalysis: WallAnalysis,
    askWallAnalysis: WallAnalysis,
    directionalAnalysis: DirectionalAnalysis
  ): EnhancedImbalanceScore {
    const reasoning: string[] = [];
    let totalScore = 0;

    // Component 1: Depth Weight Score (max 30 points)
    const depthWeightScore = Math.min(
      (bidWallAnalysis.totalWeightedVolume + askWallAnalysis.totalWeightedVolume) / 50000 * 30,
      30
    );
    totalScore += depthWeightScore;
    if (depthWeightScore > 15) {
      reasoning.push(`Significant depth-weighted volume detected (${depthWeightScore.toFixed(1)} points)`);
    }

    // Component 2: Wall Type Score (max 25 points)
    const wallTypeScore = Math.abs(bidWallAnalysis.score - askWallAnalysis.score) / 10;
    totalScore += Math.min(wallTypeScore, 25);
    if (wallTypeScore > 10) {
      const strongerSide = bidWallAnalysis.score > askWallAnalysis.score ? 'bid' : 'ask';
      reasoning.push(`Strong ${strongerSide} wall advantage (${wallTypeScore.toFixed(1)} points)`);
    }

    // Component 3: Persistence Score (max 20 points)
    const persistenceScore = (bidWallAnalysis.persistentWalls + askWallAnalysis.persistentWalls) * 4;
    totalScore += Math.min(persistenceScore, 20);
    if (persistenceScore > 8) {
      reasoning.push(`${bidWallAnalysis.persistentWalls + askWallAnalysis.persistentWalls} persistent walls detected`);
    }

    // Component 4: Clustering Score (max 15 points)
    const clusteringScore = (directionalAnalysis.wallClusters.bidClusters + directionalAnalysis.wallClusters.askClusters) * 3;
    totalScore += Math.min(clusteringScore, 15);
    if (clusteringScore > 6) {
      reasoning.push(`Wall clustering detected (${directionalAnalysis.wallClusters.bidClusters + directionalAnalysis.wallClusters.askClusters} clusters)`);
    }

    // Determine detection and signal (more lenient threshold)
    const detected = totalScore >= 30; // Lowered threshold for detection
    let signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';

    if (detected) {
      // More lenient thresholds for signal detection
      if (directionalAnalysis.dominantSide === 'BID' && directionalAnalysis.imbalanceRatio > 1.3) {
        signal = 'BULLISH';
        reasoning.push('Bid wall dominance suggests bullish pressure');
      } else if (directionalAnalysis.dominantSide === 'ASK' && directionalAnalysis.imbalanceRatio < 0.77) {
        signal = 'BEARISH';
        reasoning.push('Ask wall dominance suggests bearish pressure');
      } else if (directionalAnalysis.dominantSide === 'BID') {
        signal = 'BULLISH';
        reasoning.push('Moderate bid wall advantage');
      } else if (directionalAnalysis.dominantSide === 'ASK') {
        signal = 'BEARISH';
        reasoning.push('Moderate ask wall advantage');
      }
    }

    // Calculate confidence based on multiple factors
    const confidence = Math.min(totalScore / 90 * 100, 100); // Max score is 90

    return {
      detected,
      signal,
      confidence,
      totalScore,
      components: {
        depthWeightScore,
        wallTypeScore,
        persistenceScore,
        clusteringScore
      },
      reasoning
    };
  }

  // Analyze price action microstructure
  private async analyzePriceActionMicrostructure(symbol: string) {
    try {
      const orderBook = this.binanceService.getCachedOrderBook(symbol);
      if (!orderBook || !orderBook.bids.length || !orderBook.asks.length) {
        return null;
      }

      const bestBid = orderBook.bids[0].price;
      const bestAsk = orderBook.asks[0].price;
      const spread = bestAsk - bestBid;
      const spreadPercent = (spread / bestBid) * 100;

      // Get historical spread data
      const spreadKey = `${symbol}_spread`;
      let spreadHistory = this.priceHistory.get(spreadKey) || [];
      spreadHistory.push(spread);

      if (spreadHistory.length > 10) {
        spreadHistory = spreadHistory.slice(-10);
      }

      this.priceHistory.set(spreadKey, spreadHistory);

      if (spreadHistory.length < 3) return null;

      // Calculate spread change
      const avgSpread = spreadHistory.reduce((sum, s) => sum + s, 0) / spreadHistory.length;
      const spreadChange = (spread - avgSpread) / avgSpread;

      // Detect unusual order sizes
      const avgBidSize = orderBook.bids.reduce((sum: number, bid: any) => sum + bid.quantity, 0) / orderBook.bids.length;
      const avgAskSize = orderBook.asks.reduce((sum: number, ask: any) => sum + ask.quantity, 0) / orderBook.asks.length;

      const unusualBids = orderBook.bids.filter((bid: any) => bid.quantity > avgBidSize * 3);
      const unusualAsks = orderBook.asks.filter((ask: any) => ask.quantity > avgAskSize * 3);

      // Detect potential iceberg orders (multiple similar-sized orders at similar prices)
      const icebergBids = this.detectIcebergOrders(orderBook.bids);
      const icebergAsks = this.detectIcebergOrders(orderBook.asks);

      const detected = Math.abs(spreadChange) > 0.2 || unusualBids.length > 2 || unusualAsks.length > 2 || icebergBids.length > 0 || icebergAsks.length > 0;

      return {
        detected,
        spread,
        spreadPercent,
        spreadChange,
        unusualBids: unusualBids.length,
        unusualAsks: unusualAsks.length,
        icebergBids: icebergBids.length,
        icebergAsks: icebergAsks.length,
        signal: spreadChange < -0.1 ? 'BULLISH' : spreadChange > 0.1 ? 'BEARISH' : 'NEUTRAL'
      };

    } catch (error) {
      logError(`Error analyzing price action microstructure for ${symbol}`, error as Error);
      return null;
    }
  }

  // Detect iceberg orders (hidden large orders split into smaller chunks)
  private detectIcebergOrders(orders: any[]) {
    const icebergs = [];
    const tolerance = 0.001; // 0.1% price tolerance

    for (let i = 0; i < orders.length - 2; i++) {
      const order1 = orders[i];
      const order2 = orders[i + 1];
      const order3 = orders[i + 2];

      // Check if orders are at similar prices and similar sizes
      const price1 = order1.price;
      const price2 = order2.price;
      const price3 = order3.price;

      const size1 = order1.quantity;
      const size2 = order2.quantity;
      const size3 = order3.quantity;

      const priceRange = Math.max(price1, price2, price3) - Math.min(price1, price2, price3);
      const avgPrice = (price1 + price2 + price3) / 3;
      const priceVariation = priceRange / avgPrice;

      const sizeRange = Math.max(size1, size2, size3) - Math.min(size1, size2, size3);
      const avgSize = (size1 + size2 + size3) / 3;
      const sizeVariation = sizeRange / avgSize;

      // Potential iceberg if prices are close and sizes are similar
      if (priceVariation < tolerance && sizeVariation < 0.2) {
        icebergs.push({
          startPrice: Math.min(price1, price2, price3),
          endPrice: Math.max(price1, price2, price3),
          avgSize: avgSize,
          totalSize: size1 + size2 + size3
        });
      }
    }

    return icebergs;
  }

  // Phase 3: Whale Activity Detection (Max 40 points total)
  private async analyzePhase3(symbol: string) {
    const results = {
      whaleActivity: null as any,
      score: 0,
      overallSignal: 'NEUTRAL' as 'BULLISH' | 'BEARISH' | 'NEUTRAL'
    };

    try {
      // Use comprehensive Binance whale detection
      const whaleAnalysis = await this.whaleDetectionService.detectWhaleActivity(symbol);

      if (whaleAnalysis && whaleAnalysis.detected) {
        results.whaleActivity = {
          detected: true,
          confidence: whaleAnalysis.confidence,
          transferDirection: whaleAnalysis.transferDirection,
          estimatedValue: whaleAnalysis.estimatedValue,
          largeOrders: whaleAnalysis.whaleActivity.largeOrders,
          largeTrades: whaleAnalysis.whaleActivity.largeTrades,
          volumeSpike: whaleAnalysis.whaleActivity.volumeSpike,
          orderBookImbalance: whaleAnalysis.whaleActivity.orderBookImbalance,
          priceImpact: whaleAnalysis.whaleActivity.priceImpact,
          hybridAnalysis: whaleAnalysis.hybridAnalysis
        };

        // Score based on whale detection confidence and activity level (max 40 points)
        results.score = Math.round(whaleAnalysis.score * 0.4); // Max 40 points (100 * 0.4)

        // Determine directional signal based on whale transfer direction
        if (whaleAnalysis.transferDirection === 'ACCUMULATION') {
          results.overallSignal = 'BULLISH'; // Accumulation is bullish
        } else if (whaleAnalysis.transferDirection === 'DISTRIBUTION') {
          results.overallSignal = 'BEARISH'; // Distribution is bearish
        } else if (whaleAnalysis.transferDirection === 'LARGE_TRANSFER') {
          // For LARGE_TRANSFER, use hybrid analysis signal if available
          const hybridSignal = whaleAnalysis.hybridAnalysis?.overallSignal;
          if (hybridSignal === 'BULLISH') {
            results.overallSignal = 'BULLISH';
          } else if (hybridSignal === 'BEARISH') {
            results.overallSignal = 'BEARISH';
          } else {
            results.overallSignal = 'NEUTRAL'; // No clear direction
          }
        } else {
          results.overallSignal = 'NEUTRAL'; // Unknown or neutral direction
        }
      }

    } catch (error) {
      logError(`Error in Phase 3 analysis for ${symbol}`, error as Error);
    }

    logInfo(`Phase 3 Analysis for ${symbol}:`, {
      whaleActivity: results.whaleActivity?.detected || false,
      confidence: results.whaleActivity?.confidence || 0,
      transferDirection: results.whaleActivity?.transferDirection || 'UNKNOWN',
      overallSignal: results.overallSignal,
      estimatedValue: results.whaleActivity?.estimatedValue || 0,
      score: results.score
    });

    return results;
  }



  // Calculate overall alert from all phases
  private calculateOverallAlert(
    symbol: string,
    currentPrice: number,
    volume24h: number,
    priceChange24h: number,
    phase1: any,
    phase2: any,
    phase3: any
  ): EarlyWarningAlert | null {

    // Initialize alert type and triggered by array
    let alertType: 'PUMP_LIKELY' | 'DUMP_LIKELY' | 'NEUTRAL' = 'NEUTRAL';
    const triggeredBy: string[] = [];

    // Debug logging to understand what's happening
    logInfo(`Calculating overall alert for ${symbol}:`, {
      phase1Score: phase1.score,
      phase2Score: phase2.score,
      phase3Score: phase3.score,
      multiTimeframeDetected: phase1.multiTimeframeVolumeAnalysis?.detected,
      multiTimeframeSignal: phase1.multiTimeframeVolumeAnalysis?.overallSignal,
      volumeSpikeDetected: phase1.volumeSpike?.detected,
      volumeSpikeSignal: phase1.volumeSpike?.signal,
      bidAskDetected: phase2.bidAskImbalance?.detected,
      bidAskSignal: phase2.bidAskImbalance?.signal,
      whaleDetected: phase3.whaleActivity?.detected,
      whaleDirection: phase3.whaleActivity?.transferDirection
    });

    // Build triggered by array based on detected signals
    if (phase1.multiTimeframeVolumeAnalysis?.detected && phase1.multiTimeframeVolumeAnalysis.overallSignal !== 'NEUTRAL') {
      const volumeAnalysis = phase1.multiTimeframeVolumeAnalysis;
      triggeredBy.push(`Multi-Timeframe Volume Analysis (${volumeAnalysis.timeframes.length} timeframes)`);

      // Add strongest timeframe to triggered by
      if (volumeAnalysis.strongestTimeframe) {
        const strongestAnalysis = volumeAnalysis.timeframes.find(
          (tf: BuySellVolumeAnalysis) => tf.timeframe === volumeAnalysis.strongestTimeframe
        );
        if (strongestAnalysis) {
          triggeredBy.push(`Strongest: ${strongestAnalysis.timeframe} (${strongestAnalysis.dominantDirection})`);
        }
      }
    }
    // Fallback to legacy volume spike for backward compatibility
    else if (phase1.volumeSpike?.detected && phase1.volumeSpike.signal !== 'NEUTRAL') {
      triggeredBy.push('Volume Spike');
    }

    if (phase1.rsiMomentum?.detected && phase1.rsiMomentum.momentum !== 'NEUTRAL') {
      triggeredBy.push('RSI Momentum');
    }

    // Note: EMA Convergence is removed from triggering alerts since it doesn't provide directional signals
    // It only indicates potential volatility, not pump/dump direction

    // Calculate time estimates based on confidence and signals
    let timeEstimateMin = 3;
    let timeEstimateMax = 8;

    // Adjust time estimates based on multi-timeframe volume analysis
    if (phase1.multiTimeframeVolumeAnalysis?.detected) {
      const volumeAnalysis = phase1.multiTimeframeVolumeAnalysis;
      const strongestTimeframe = volumeAnalysis.strongestTimeframe;

      // Shorter timeframes indicate faster moves
      if (strongestTimeframe === '5m') {
        timeEstimateMin = 1;
        timeEstimateMax = 3;
      } else if (strongestTimeframe === '15m') {
        timeEstimateMin = 2;
        timeEstimateMax = 5;
      } else if (strongestTimeframe === '1h') {
        timeEstimateMin = 3;
        timeEstimateMax = 8;
      } else if (strongestTimeframe === '4h') {
        timeEstimateMin = 5;
        timeEstimateMax = 12;
      } else if (strongestTimeframe === '1d') {
        timeEstimateMin = 8;
        timeEstimateMax = 24;
      }

      // Adjust based on signal strength
      if (volumeAnalysis.score > 50) {
        timeEstimateMin = Math.max(0.5, timeEstimateMin * 0.7);
        timeEstimateMax = Math.max(1, timeEstimateMax * 0.8);
      }
    }
    // Fallback to legacy volume spike logic
    else if (phase1.volumeSpike?.detected && phase1.volumeSpike.ratio > 3) {
      timeEstimateMin = 2;
      timeEstimateMax = 5;
    }

    if (phase2.score > 0) {
      timeEstimateMin = 1;
      timeEstimateMax = 3;
    }

    if (phase3.score > 0) {
      timeEstimateMin = 0.5;
      timeEstimateMax = 2;
    }

    // Add Phase 2 signals to triggered by
    if (phase2.bidAskImbalance?.detected && phase2.bidAskImbalance.signal !== 'NEUTRAL') {
      triggeredBy.push('Order Flow Imbalance');
    }

    if (phase2.priceAction?.detected && phase2.priceAction.signal !== 'NEUTRAL') {
      triggeredBy.push('Price Action Microstructure');
    }

    // Add Phase 3 signals to triggered by
    if (phase3.whaleActivity?.detected && phase3.whaleActivity.transferDirection !== 'NEUTRAL' && phase3.whaleActivity.transferDirection !== 'UNKNOWN') {
      triggeredBy.push('Whale Activity');
    }

    // Calculate directional confidence using fixed phase allocations (max 100%)
    // Phase 1 (Volume & Momentum): max 25 points
    // Phase 2 (Order Flow): max 35 points
    // Phase 3 (Whale Activity): max 40 points
    // BULLISH signals add positive points, BEARISH signals subtract points
    let directionalScore = 0;
    let confidence = 0;

    if (triggeredBy.length > 0) {
      // Phase 1 contribution (max ±25 points)
      let phase1Contribution = 0;
      if (phase1.overallSignal && phase1.overallSignal !== 'NEUTRAL') {
        // Use the actual Phase 1 score (already capped at 25 points)
        const phase1Score = Math.min(phase1.score, 25);
        // Apply directional scoring: BULLISH = positive, BEARISH = negative
        phase1Contribution = phase1.overallSignal === 'BULLISH'
          ? phase1Score
          : -phase1Score;
      }

      // Phase 2 contribution (max ±35 points)
      let phase2Contribution = 0;
      if (phase2.overallSignal && phase2.overallSignal !== 'NEUTRAL') {
        // Use the actual Phase 2 score (already capped at 35 points)
        const phase2Score = Math.min(phase2.score, 35);
        // Apply directional scoring: BULLISH = positive, BEARISH = negative
        phase2Contribution = phase2.overallSignal === 'BULLISH'
          ? phase2Score
          : -phase2Score;
      }

      // Phase 3 contribution (max ±40 points)
      let phase3Contribution = 0;
      if (phase3.overallSignal && phase3.overallSignal !== 'NEUTRAL') {
        // Use the actual Phase 3 score (already capped at 40 points)
        const phase3Score = Math.min(phase3.score, 40);
        // Apply directional scoring: BULLISH = positive, BEARISH = negative
        phase3Contribution = phase3.overallSignal === 'BULLISH'
          ? phase3Score
          : -phase3Score;
      }

      // Calculate net directional score
      directionalScore = phase1Contribution + phase2Contribution + phase3Contribution;

      // Confidence is the absolute value of the directional score
      confidence = Math.round(Math.abs(directionalScore));

      // Determine alert type based on directional score
      if (directionalScore > 0) {
        alertType = 'PUMP_LIKELY';
      } else if (directionalScore < 0) {
        alertType = 'DUMP_LIKELY';
      } else {
        alertType = 'NEUTRAL';
        confidence = 0;
      }
    }

    // Final check: If no significant signals were detected, ensure alert type is NEUTRAL
    if (triggeredBy.length === 0) {
      alertType = 'NEUTRAL';
      confidence = 0; // Explicitly set confidence to 0 for NEUTRAL signals
    }

    // Ensure volumeSpike has proper structure with default values if missing
    const volumeSpikeData = phase1.volumeSpike ? {
      detected: phase1.volumeSpike.detected || false,
      // Use the highest ratio between buy and sell volume spikes
      spikeRatio: Math.max(phase1.volumeSpike.buyVolumeRatio || 0, phase1.volumeSpike.sellVolumeRatio || 0),
      // Use the combined current and average volumes from multi-timeframe analysis
      currentVolume: phase1.volumeSpike.currentVolume || 0,
      averageVolume: phase1.volumeSpike.averageVolume || 0,
      signal: phase1.volumeSpike.signal || 'NEUTRAL'
    } : {
      detected: false,
      spikeRatio: 0,
      currentVolume: 0,
      averageVolume: 0,
      signal: 'NEUTRAL'
    };

    // Debug log the final alert before returning
    logInfo(`Final alert for ${symbol}:`, {
      alertType,
      confidence,
      triggeredBy,
      volumeSpikeData,
      directionalScoring: {
        netDirectionalScore: directionalScore,
        phase1Signal: phase1.overallSignal || 'NEUTRAL',
        phase2Signal: phase2.overallSignal || 'NEUTRAL',
        phase3Signal: phase3.overallSignal || 'NEUTRAL',
        phaseContributions: {
          phase1: phase1.overallSignal === 'BULLISH' ? phase1.score :
                  phase1.overallSignal === 'BEARISH' ? -phase1.score : 0,
          phase2: phase2.overallSignal === 'BULLISH' ? phase2.score :
                  phase2.overallSignal === 'BEARISH' ? -phase2.score : 0,
          phase3: phase3.overallSignal === 'BULLISH' ? phase3.score :
                  phase3.overallSignal === 'BEARISH' ? -phase3.score : 0
        },
        originalScores: {
          phase1: phase1.score,
          phase2: phase2.score,
          phase3: phase3.score
        }
      }
    });

    return {
      symbol,
      alertType,
      confidence,
      timeEstimateMin,
      timeEstimateMax,
      triggeredBy,
      currentPrice,
      volume24h,
      priceChange24h,
      volumeSpike: volumeSpikeData,
      rsiMomentum: phase1.rsiMomentum,
      emaConvergence: phase1.emaConvergence,
      bidAskImbalance: phase2.bidAskImbalance,
      priceAction: phase2.priceAction,
      whaleActivity: phase3.whaleActivity,
      phase1Score: phase1.score,
      phase2Score: phase2.score,
      phase3Score: phase3.score
    };
  }

  // Check if we should create alert (prevent spam)
  private shouldCreateAlert(symbol: string, alertType: string): boolean {
    const key = `${symbol}_${alertType}`;
    const lastAlert = this.lastAlerts.get(key) || 0;
    const now = Date.now();

    // Minimum 5 minutes between same type of alerts for same symbol
    if (now - lastAlert < 5 * 60 * 1000) {
      return false;
    }

    this.lastAlerts.set(key, now);
    return true;
  }

  // Save alert to database
  private async saveAlert(alert: EarlyWarningAlert): Promise<void> {
    try {
      const prisma = prismaService.getClient();

      await prisma.earlyWarningAlert.create({
        data: {
          symbol: alert.symbol,
          exchange: 'binance',
          alertType: alert.alertType,
          confidence: alert.confidence,
          timeEstimateMin: alert.timeEstimateMin,
          timeEstimateMax: alert.timeEstimateMax,
          volumeSpike: alert.volumeSpike,
          rsiMomentum: alert.rsiMomentum,
          emaConvergence: alert.emaConvergence,
          bidAskImbalance: alert.bidAskImbalance,
          priceAction: alert.priceAction,
          whaleActivity: alert.whaleActivity,
          phase1Score: alert.phase1Score,
          phase2Score: alert.phase2Score,
          phase3Score: alert.phase3Score,
          triggeredBy: alert.triggeredBy,
          currentPrice: alert.currentPrice,
          volume24h: alert.volume24h || 0,
          priceChange24h: alert.priceChange24h || 0,
          isActive: true,
          isResolved: false
        }
      });

      logInfo(`Early warning alert saved for ${alert.symbol}: ${alert.alertType} (${alert.confidence}% confidence)`);

    } catch (error) {
      logError(`Error saving early warning alert for ${alert.symbol}`, error as Error);
    }
  }

  // Note: Broadcasting is now handled by the early warning alert rules endpoint
  // which ensures rule-based filtering and prevents duplicate alerts

  // Get active alerts for a symbol
  async getActiveAlerts(symbol?: string): Promise<any[]> {
    try {
      const prisma = prismaService.getClient();

      const where: any = {
        isActive: true,
        isResolved: false,
        createdAt: {
          gte: new Date(Date.now() - 60 * 60 * 1000) // Last hour
        }
      };

      if (symbol) {
        where.symbol = symbol;
      }

      const alerts = await prisma.earlyWarningAlert.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: 50
      });

      return alerts;

    } catch (error) {
      logError('Error fetching active early warning alerts', error as Error);
      return [];
    }
  }

  // Get alert history
  async getAlertHistory(symbol?: string, limit: number = 100): Promise<any[]> {
    try {
      const prisma = prismaService.getClient();

      const where: any = {};
      if (symbol) {
        where.symbol = symbol;
      }

      const alerts = await prisma.earlyWarningAlert.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit
      });

      return alerts;

    } catch (error) {
      logError('Error fetching early warning alert history', error as Error);
      return [];
    }
  }

  // Update alert outcome for accuracy tracking
  async updateAlertOutcome(alertId: string, outcome: string, responseTime?: number): Promise<void> {
    try {
      const prisma = prismaService.getClient();

      // Calculate accuracy score based on outcome
      let accuracyScore = 0;
      switch (outcome) {
        case 'PUMP_CONFIRMED':
        case 'DUMP_CONFIRMED':
          accuracyScore = 100;
          break;
        case 'PARTIAL_MOVE':
          accuracyScore = 50;
          break;
        case 'FALSE_SIGNAL':
          accuracyScore = 0;
          break;
      }

      await prisma.earlyWarningAlert.update({
        where: { id: alertId },
        data: {
          isResolved: true,
          resolvedAt: new Date(),
          actualOutcome: outcome,
          accuracyScore,
          responseTime
        }
      });

      logInfo(`Updated alert outcome: ${alertId} -> ${outcome} (${accuracyScore}% accuracy)`);

    } catch (error) {
      logError(`Error updating alert outcome for ${alertId}`, error as Error);
    }
  }
}
