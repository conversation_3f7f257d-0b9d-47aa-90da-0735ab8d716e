<!DOCTYPE html>
<html>
<head>
    <title>Generate Notification Sounds</title>
</head>
<body>
    <h1>Generate Notification Sounds</h1>
    <p>Click the buttons below to generate and download sound files for your notification system:</p>
    
    <button onclick="generateNotificationSound()">Generate Notification Sound (notification-sound.wav)</button>
    <br><br>
    <button onclick="generateEarlyWarningSound()">Generate Early Warning Sound (early-warning-sound.wav)</button>
    <br><br>
    <button onclick="generateBothSounds()">Generate Both Sound Files</button>
    
    <script>
        function generateNotificationSound() {
            // Create a gentle notification sound (higher pitch, shorter)
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const duration = 0.5; // 0.5 seconds
            const sampleRate = audioContext.sampleRate;
            const numSamples = duration * sampleRate;
            
            const buffer = audioContext.createBuffer(1, numSamples, sampleRate);
            const channelData = buffer.getChannelData(0);
            
            // Generate a pleasant notification tone (800Hz + 1200Hz)
            for (let i = 0; i < numSamples; i++) {
                const t = i / sampleRate;
                const envelope = Math.exp(-t * 3); // Fade out
                channelData[i] = envelope * (
                    0.3 * Math.sin(2 * Math.PI * 800 * t) +
                    0.2 * Math.sin(2 * Math.PI * 1200 * t)
                );
            }
            
            // Convert to WAV and download
            downloadAudio(buffer, 'notification-sound.wav');
        }
        
        function generateEarlyWarningSound() {
            // Create an urgent warning sound (lower pitch, longer, more intense)
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const duration = 1.0; // 1 second
            const sampleRate = audioContext.sampleRate;
            const numSamples = duration * sampleRate;
            
            const buffer = audioContext.createBuffer(1, numSamples, sampleRate);
            const channelData = buffer.getChannelData(0);
            
            // Generate an urgent warning tone (400Hz + 600Hz with modulation)
            for (let i = 0; i < numSamples; i++) {
                const t = i / sampleRate;
                const envelope = Math.exp(-t * 1.5); // Slower fade out
                const modulation = 1 + 0.3 * Math.sin(2 * Math.PI * 8 * t); // 8Hz modulation
                channelData[i] = envelope * modulation * (
                    0.4 * Math.sin(2 * Math.PI * 400 * t) +
                    0.3 * Math.sin(2 * Math.PI * 600 * t) +
                    0.1 * Math.sin(2 * Math.PI * 200 * t)
                );
            }
            
            // Convert to WAV and download
            downloadAudio(buffer, 'early-warning-sound.wav');
        }

        function generateBothSounds() {
            generateNotificationSound();
            setTimeout(() => generateEarlyWarningSound(), 500);
        }
        
        function downloadAudio(buffer, filename) {
            // Convert AudioBuffer to WAV
            const wav = audioBufferToWav(buffer);
            const blob = new Blob([wav], { type: 'audio/wav' });
            
            // Create download link
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            const channelData = buffer.getChannelData(0);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, buffer.sampleRate, true);
            view.setUint32(28, buffer.sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);
            
            // Convert float samples to 16-bit PCM
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, channelData[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
            
            return arrayBuffer;
        }
    </script>
</body>
</html>
