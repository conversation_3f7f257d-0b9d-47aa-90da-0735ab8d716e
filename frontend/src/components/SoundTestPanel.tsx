'use client';

import { useState } from 'react';
import { Volume2, VolumeX, Play } from 'lucide-react';
import {
  testNotificationSound,
  testEarlyWarningSound,
  enableSounds,
  SoundType
} from '../utils/soundManager';

export default function SoundTestPanel() {
  const [soundsEnabled, setSoundsEnabled] = useState(true);
  const [testing, setTesting] = useState<string | null>(null);

  const handleToggleSounds = () => {
    const newState = !soundsEnabled;
    setSoundsEnabled(newState);
    enableSounds(newState);
  };

  const handleTestSound = async (soundType: SoundType, label: string) => {
    setTesting(label);
    try {
      if (soundType === SoundType.NOTIFICATION) {
        await testNotificationSound();
      } else {
        await testEarlyWarningSound();
      }
    } catch (error) {
      console.error(`Failed to test ${label} sound:`, error);
    } finally {
      setTimeout(() => setTesting(null), 1000);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Sound Test Panel</h3>
        <button
          onClick={handleToggleSounds}
          className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
            soundsEnabled 
              ? 'bg-green-100 text-green-700 hover:bg-green-200' 
              : 'bg-red-100 text-red-700 hover:bg-red-200'
          }`}
        >
          {soundsEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          <span className="text-sm font-medium">
            {soundsEnabled ? 'Sounds On' : 'Sounds Off'}
          </span>
        </button>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-2">
            <div>
              <h4 className="font-medium text-blue-900">Notification Sound</h4>
              <p className="text-sm text-blue-700">Regular notification alerts</p>
            </div>
            <button
              onClick={() => handleTestSound(SoundType.NOTIFICATION, 'Notification')}
              disabled={testing === 'Notification'}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Play className="h-4 w-4" />
              <span className="text-sm">
                {testing === 'Notification' ? 'Playing...' : 'Test'}
              </span>
            </button>
          </div>
          <div className="text-xs text-blue-600">
            Higher pitch, shorter duration, gentle tone
          </div>
        </div>

        <div className="p-4 bg-red-50 rounded-lg border border-red-200">
          <div className="flex items-center justify-between mb-2">
            <div>
              <h4 className="font-medium text-red-900">Early Warning Sound</h4>
              <p className="text-sm text-red-700">Urgent early warning alerts</p>
            </div>
            <button
              onClick={() => handleTestSound(SoundType.EARLY_WARNING, 'Early Warning')}
              disabled={testing === 'Early Warning'}
              className="flex items-center space-x-2 px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Play className="h-4 w-4" />
              <span className="text-sm">
                {testing === 'Early Warning' ? 'Playing...' : 'Test'}
              </span>
            </button>
          </div>
          <div className="text-xs text-red-600">
            Lower pitch, longer duration, urgent tone with modulation
          </div>
        </div>
      </div>

      <div className="mt-6 p-3 bg-gray-50 rounded-lg">
        <h5 className="font-medium text-gray-900 mb-2">Sound Files</h5>
        <div className="text-xs text-gray-600 space-y-1">
          <div>• Notification: /notification-sound.wav</div>
          <div>• Early Warning: /early-warning-sound.wav</div>
          <div className="mt-2 text-gray-500">
            If sound files are missing, fallback Web Audio API sounds will be used.
          </div>
        </div>
      </div>

      <div className="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
        <div className="text-xs text-yellow-800">
          <strong>Note:</strong> To generate sound files, open{' '}
          <code className="bg-yellow-100 px-1 rounded">/generate-sounds.html</code>{' '}
          in your browser and download the generated WAV files to the public directory.
        </div>
      </div>
    </div>
  );
}
