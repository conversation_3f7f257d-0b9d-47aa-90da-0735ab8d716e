/**
 * Sound Manager for handling different notification sounds
 */

export enum SoundType {
  NOTIFICATION = 'notification',
  EARLY_WARNING = 'early_warning'
}

interface SoundConfig {
  path: string;
  volume: number;
  description: string;
}

class SoundManager {
  private sounds: Map<SoundType, SoundConfig> = new Map();
  private audioCache: Map<string, HTMLAudioElement> = new Map();
  private isEnabled: boolean = true;

  constructor() {
    // Configure different sounds
    this.sounds.set(SoundType.NOTIFICATION, {
      path: '/notification-sound.wav',
      volume: 0.6,
      description: 'Regular notification sound'
    });

    this.sounds.set(SoundType.EARLY_WARNING, {
      path: '/early-warning-sound.wav',
      volume: 0.8,
      description: 'Early warning alert sound'
    });
  }

  /**
   * Enable or disable all sounds
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Check if sounds are enabled
   */
  isAudioEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * Preload a sound file
   */
  private async preloadSound(soundPath: string): Promise<HTMLAudioElement> {
    if (this.audioCache.has(soundPath)) {
      return this.audioCache.get(soundPath)!;
    }

    return new Promise((resolve, reject) => {
      const audio = new Audio(soundPath);
      audio.preload = 'auto';

      audio.addEventListener('canplaythrough', () => {
        this.audioCache.set(soundPath, audio);
        resolve(audio);
      });

      audio.addEventListener('error', (e) => {
        console.warn(`Failed to load sound: ${soundPath}`, e);
        reject(e);
      });

      // Fallback: resolve after timeout even if not fully loaded
      setTimeout(() => {
        if (!this.audioCache.has(soundPath)) {
          this.audioCache.set(soundPath, audio);
          resolve(audio);
        }
      }, 2000);
    });
  }

  /**
   * Play a specific sound type
   */
  async playSound(soundType: SoundType): Promise<void> {
    if (!this.isEnabled) {
      console.log(`Sound disabled, skipping ${soundType} sound`);
      return;
    }

    const soundConfig = this.sounds.get(soundType);
    if (!soundConfig) {
      console.warn(`Sound configuration not found for type: ${soundType}`);
      return;
    }

    try {
      const audio = await this.preloadSound(soundConfig.path);

      // Reset audio to beginning and set volume
      audio.currentTime = 0;
      audio.volume = soundConfig.volume;

      // Play the sound
      const playPromise = audio.play();

      if (playPromise !== undefined) {
        await playPromise;
        console.log(`Played ${soundType} sound: ${soundConfig.description}`);
      }
    } catch (error) {
      console.warn(`Failed to play ${soundType} sound:`, error);

      // Fallback: try to generate a beep sound using Web Audio API
      this.playFallbackBeep(soundType);
    }
  }

  /**
   * Fallback beep sound using Web Audio API
   */
  private playFallbackBeep(soundType: SoundType): void {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Different frequencies for different sound types
      if (soundType === SoundType.EARLY_WARNING) {
        // Lower, more urgent tone for early warning
        oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8);
        oscillator.stop(audioContext.currentTime + 0.8);
      } else {
        // Higher, gentler tone for regular notifications
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(1200, audioContext.currentTime + 0.1);
        gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.4);
        oscillator.stop(audioContext.currentTime + 0.4);
      }

      oscillator.start();
      console.log(`Played fallback beep for ${soundType}`);
    } catch (error) {
      console.warn('Failed to play fallback beep:', error);
    }
  }

  /**
   * Test a specific sound
   */
  async testSound(soundType: SoundType): Promise<void> {
    console.log(`Testing ${soundType} sound...`);
    await this.playSound(soundType);
  }

  /**
   * Update sound configuration
   */
  updateSoundConfig(soundType: SoundType, config: Partial<SoundConfig>): void {
    const currentConfig = this.sounds.get(soundType);
    if (currentConfig) {
      this.sounds.set(soundType, { ...currentConfig, ...config });
    }
  }

  /**
   * Get current sound configuration
   */
  getSoundConfig(soundType: SoundType): SoundConfig | undefined {
    return this.sounds.get(soundType);
  }

  /**
   * Clear audio cache
   */
  clearCache(): void {
    this.audioCache.clear();
  }
}

// Export singleton instance
export const soundManager = new SoundManager();

// Convenience functions
export const playNotificationSound = () => soundManager.playSound(SoundType.NOTIFICATION);
export const playEarlyWarningSound = () => soundManager.playSound(SoundType.EARLY_WARNING);
export const enableSounds = (enabled: boolean) => soundManager.setEnabled(enabled);
export const testNotificationSound = () => soundManager.testSound(SoundType.NOTIFICATION);
export const testEarlyWarningSound = () => soundManager.testSound(SoundType.EARLY_WARNING);
